variable "policies" {
  description = "values for policies"
  type = map(object({
    type            = optional(string, "BuiltIn") # BuiltIn, Custom
    constraint_name = optional(string)            # for BuiltIn policies
    name            = optional(string)            # for Custom policies - constraint name
    display_name    = optional(string)            # for Custom policies
    description     = optional(string)            # for Custom policies
    spec            = optional(any, {})
  }))
  default = {}

  validation {
    condition = alltrue([
      for p in var.policies : contains(["BuiltIn", "Custom"], p.type)
    ])
    error_message = "The 'type' attribute for each policy must be either 'BuiltIn' or 'Custom'."
  }
}

variable "organization_id" {
  description = "The organization ID to deploy policies to. Mutually exclusive with folder_id."
  type        = string
  default     = null
  validation {
    condition     = (var.organization_id != null && var.folder_id == null) || (var.organization_id == null && var.folder_id != null)
    error_message = "Exactly one of organization_id or folder_id must be set."
  }
}

variable "folder_id" {
  description = "The folder ID to deploy policies to. Mutually exclusive with organization_id."
  type        = string
  default     = null
}

variable "target" {
  description = "Target for policy merging operations."
  type        = string
}
