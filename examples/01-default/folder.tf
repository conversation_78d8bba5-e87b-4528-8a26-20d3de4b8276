module "organization_policy" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  source = "../.."
  #source            = "git::https://<EMAIL>/ ... ref=v0.1.0"
  context = module.context.context

  # Only one of organization_id or folder_id can be set per deployment.
  # organization_id = null
  folder_id = "336467911323"

  # The 'target' field is used only for policy merging logic, not for deployment targeting.
  target = "lztest"

  # Policy configurations
  policies = {

    # Example with SSH in browser - different for network vs others
    disable_ssh_in_browser = {
      type            = "BuiltIn"
      constraint_name = "compute.managed.restrictDiskCreation"
      spec = {
        default = {
          enforce = "true"
          parameters = jsonencode({"isSizeLimitCheck" : true, "allowedDiskTypes" : ["pd-ssd", "pd-standard"]})

        }
        org     = {}
        audit   = {}
        core    = {}
        lzprod  = {}
        lztest  = {} # Will use default enforce = "TRUE"
        managmt = {}
        network = {
          enforce = "false"
        }
      }
    }

    # List constraint example with allowed values
    domain_restricted_sharing = {
      type            = "BuiltIn"
      constraint_name = "iam.allowedPolicyMemberDomains"
      spec = {
        default = {
          allow_all           = "TRUE"
          denied_values       = "principalSet://iam.googleapis.com/folders/336467911323, C02nnx01r"
          inherit_from_parent = "TRUE"
        }
        org     = {}
        audit   = {}
        core    = {}
        lzprod  = {}
        lztest  = {
          inherit_from_parent = "FALSE" # Override for lztest
          exclude_projects = "otp-diszi-demoday-prj-edv-01, otp-diszi-iactest-prj-edv-01"
        }
        managmt = {}
        network = {}
      }
    }

    # Boolean constraint with condition example
    enforce_uniform_bucket_access_conditional = {
      type            = "BuiltIn"
      constraint_name = "storage.uniformBucketLevelAccess"
      spec = {
        default = {
          enforce = "TRUE"
          condition = "has(resource.labels.environment) && resource.labels.environment in ['prod', 'staging']"
        }
        org     = {}
        audit   = {}
        core    = {}
        lzprod  = {}
        lztest  = {}
        managmt = {}
        network = {}
      }
    }

    # Example of policy that will be skipped for certain targets
    example_policy_not_for_audit = {
      type            = "BuiltIn"
      constraint_name = "compute.disableSerialPortLogging"
      spec = {
        default = {
          enforce = "TRUE"
        }
        org     = {}
        audit   = {}
        core    = {}
        lzprod  = {}
        # lztest  = {} # Skip this policy for lztest
        managmt = {}
        network = {}
      }
    }
  }
}
