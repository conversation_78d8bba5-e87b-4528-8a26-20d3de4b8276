# Custom constraint examples demonstrating various CEL conditions and use cases
# This shows how to create custom organization policy constraints

module "organization_policy_custom" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  source = "../.."
  #source            = "git::https://<EMAIL>/ ... ref=v0.1.0"
  # Deployment target: specify exactly one of organization_id or folder_id
  # Required by module interface, but project/context targeting is deprecated.
  organization_id = "226353376208"
  context = module.context.context
  target  = "org"

  # Custom constraint policy configurations
  policies = {

    require_gcs_bucket_labels = {
      type         = "Custom"
      name         = "custom.requireGCSBucketLabels"
      display_name = "Require GCS Bucket Labels"
      description  = "This constraint requires that all GCS buckets have specific labels: environment, team, and cost-center."
      spec = {
        default = {
          # CEL condition that ensures required labels are present
          condition = <<-EOT
            !has(resource.labels.environment) || 
            !has(resource.labels.team) || 
            !has(resource.labels['cost-center']) ||
            resource.labels.environment == "" ||
            resource.labels.team == "" ||
            resource.labels['cost-center'] == ""
          EOT
          
          # DENY if the required labels are missing or empty
          action_type = "DENY"

          # Apply to CREATE and UPDATE operations
          method_types = "CREATE, UPDATE"
          
          # Resource type this constraint applies to
          resource_type = "storage.googleapis.com/Bucket"
        }
        org     = {}
        audit   = {}
        core    = {}
        lzprod  = {}
        lztest  = {} # Will use default values
        managmt = {}
        network = {}
      }
    }

    # Organization policy that enforces the custom constraint
    enforce_gcs_bucket_labels = {
      type            = "BuiltIn"
      constraint_name = "custom.requireGCSBucketLabels"
      spec = {
        default = {
          enforce = "true"
        }
        org     = {}
        audit   = {}
        core    = {}
        lzprod  = {}
        lztest  = {} # Will use default enforce = "true"
        managmt = {}
        network = {}
      }
    }

  }
}
