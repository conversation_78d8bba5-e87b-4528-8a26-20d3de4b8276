#provider - All providers configured in the network-mirror/.terraformrc file must be explicitly configured with a provider block.
provider "google" {
}

#required_providers - Make sure that you are using versions, which are available on https://az-nexus.otpbank.hu/repository/nexus-terraform-provider/.
terraform {
  required_version = ">= v1.9.4"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "6.19.0"
    }
  }
}