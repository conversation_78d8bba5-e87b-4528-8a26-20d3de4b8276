module "context" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  source = "git::https://<EMAIL>/ADOS-OTPHU-01/OTPHU-COE-TEMPLATESPEC/_git/terraform-context?ref=v0.8"

  #Common
  subsidiary  = var.subsidiary
  cloud       = var.cloud
  environment = var.environment
  region      = var.region

  #General
  project    = var.project
  department = var.department
  tags = {
    OwnerOU      = "ccoe"
    OwnerDL      = var.owner_dl
    OwnerContact = var.owner //In the blueprint change the OwnerContact tag to the owner of the solution and remove variable "owner" {}.
    Criticality  = "false"
  }
}
