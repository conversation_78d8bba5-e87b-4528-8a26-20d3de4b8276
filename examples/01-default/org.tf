module "organization_policy_org" {
  #checkov:skip=CKV_TF_1: We strictly use single tags for single hash
  source = "../.."
  #source            = "git::https://<EMAIL>/ ... ref=v0.1.0"
  context = module.context.context

  # Only one of organization_id or folder_id can be set per deployment.
  organization_id = "226353376208"
  # folder_id     = null

  # The 'target' field is used only for policy merging logic, not for deployment targeting.
  target = "org"

  # Policy configurations
  policies = {

    # SSH in browser example - network has different setting
    disable_ssh_in_browser = {
      type            = "BuiltIn"
      constraint_name = "compute.disableSshInBrowser"
      spec = {
        default = {
          enforce = "true"
        }
        org = {
          exclude_folders  = "911817463245, 741641675166"
          exclude_projects = "otp-diszi-demoday-prj-edv-01"
        }
        audit   = {}
        core    = {}
        lzprod  = {}
        lztest  = {}
        managmt = {}
        network = {
          enforce = "false"
        }
      }
    }

    # List constraint example with denied values
    deny_external_load_balancers = {
      type            = "BuiltIn"
      constraint_name = "compute.restrictLoadBalancerCreationForTypes"
      spec = {
        default = {
          denied_values = "EXTERNAL,"
          deny_all      = "FALSE"
        }
        org     = {} # Will use default
        audit   = {}
        core    = {}
        lzprod  = {}
        lztest  = {}
        managmt = {}
        network = {}
      }
    }

    # List constraint with condition example
    restrict_vm_external_ip_conditional = {
      type            = "BuiltIn"
      constraint_name = "compute.vmExternalIpAccess"
      spec = {
        default = {
          denied_values = "*"
          condition     = "resource.matchTagId('tagKeys/123', 'tagValues/456')"
        }
        org     = {} # Skip this policy for org
        audit   = {}
        core    = {}
        lzprod  = {}
        lztest  = {}
        managmt = {}
        network = {}
      }
    }
  }
}
