locals {
  # Determine deployment target based on mutually exclusive variables
  is_org    = var.organization_id != null && var.folder_id == null
  is_folder = var.organization_id == null && var.folder_id != null

  # Use explicit IDs only
  target_id = local.is_org ? var.organization_id : var.folder_id

  # Policy parent string for deployment
  parent = local.is_org ? "organizations/${var.organization_id}" : "folders/${var.folder_id}"

  # Filter and merge policies where target exists
  all_policies_with_merged_spec = {
    for key, policy in var.policies : key => merge(policy, {
      # Merge default with target-specific settings
      merged_spec = merge(
        try(policy.spec.default, {}),
        try(policy.spec[var.target], {})
      )
    }) if can(policy.spec[var.target])
  }

  # Create a map of fields that should be lists
  policies_lists_map = {
    for key, policy in local.all_policies_with_merged_spec :
    key => {
      for spec_key, spec_value in policy.merged_spec :
      spec_key => (
        can(regex(",", tostring(spec_value)))
        ? [for v in split(",", tostring(spec_value)) : trim(v, " ,") if trim(v, " ,") != ""]
        : (
            can(tolist(spec_value)) ? tolist(spec_value) :
            spec_value != null && spec_value != "" ? [tostring(spec_value)] : []
          )
      )
      if contains(["allowed_values", "denied_values", "values", "method_types", "resource_types", "exclude_folders", "exclude_projects"], spec_key)
    }
  }

  # Create a map of fields that should be booleans
  policies_booleans_map = {
    for key, policy in local.all_policies_with_merged_spec :
    key => {
      for spec_key, spec_value in policy.merged_spec :
      spec_key => (
        lower(tostring(spec_value)) == "true" ? true :
        lower(tostring(spec_value)) == "false" ? false :
        null
      )
      if contains(["inherit_from_parent", "reset"], spec_key)
    }
  }

  # Create a map of fields that should be strings
  policies_strings_map = {
    for key, policy in local.all_policies_with_merged_spec :
    key => {
      for spec_key, spec_value in policy.merged_spec :
      spec_key => spec_value
      if !contains(keys(lookup(local.policies_lists_map, key, {})), spec_key)
         && !contains(keys(lookup(local.policies_booleans_map, key, {})), spec_key)
    }
  }

  # Merge all maps for each policy
  policies_with_transformed_specs = {
    for key, policy in local.all_policies_with_merged_spec :
      key => merge(policy, {
        transformed_spec = merge(
          lookup(local.policies_strings_map, key, {}),
          lookup(local.policies_lists_map, key, {}),
          lookup(local.policies_booleans_map, key, {})
        )
      })
  }

  # Filter custom policies
  custom_policies = {
    for key, policy in local.policies_with_transformed_specs : key => policy
    if policy.type == "Custom"
  }

  # Process builtin policies - determine constraint types
  builtin_policies = {
    for key, policy in local.policies_with_transformed_specs : key => merge(policy, {
      is_boolean_constraint = can(policy.transformed_spec.enforce) && !can(policy.transformed_spec.allowed_values) && !can(policy.transformed_spec.denied_values) && !can(policy.transformed_spec.allow_all) && !can(policy.transformed_spec.deny_all)
      is_list_constraint    = !can(policy.transformed_spec.enforce) && can(policy.transformed_spec.allowed_values) || can(policy.transformed_spec.denied_values) || can(policy.transformed_spec.allow_all) || can(policy.transformed_spec.deny_all)
    }) if policy.type == "BuiltIn"
  }
}
