module "org_policies" {
  source = "./modules/org_policy_policy"

  for_each = local.builtin_policies

  # Common parameters for all policies
  parent   = local.parent
  constraint_name = each.value.constraint_name
  condition = try(each.value.transformed_spec.condition, null) != null ? {
    expression  = each.value.transformed_spec.condition
    title       = null
    description = null
    location    = null
  } : null
  exclude_folders = try(each.value.transformed_spec.exclude_folders, [])
  exclude_projects = try(each.value.transformed_spec.exclude_projects, [])

  # Boolean constraint parameters
  enforce_policy             = each.value.is_boolean_constraint ? try(upper(each.value.transformed_spec.enforce) == "TRUE" ? "TRUE" : "FALSE") : null
  parameters                 = try(each.value.transformed_spec.parameters, null)
  is_boolean_constraint = each.value.is_boolean_constraint

  # List constraint parameters
  inherit_from_parent = each.value.is_list_constraint ? try(each.value.transformed_spec.inherit_from_parent, false) : false
  list_constraint = each.value.is_list_constraint ? [{
    allowed_values = try(each.value.transformed_spec.allowed_values, null)
    denied_values  = try(each.value.transformed_spec.denied_values, null)
    allow_all      = try(each.value.transformed_spec.allow_all, null)
    deny_all       = try(each.value.transformed_spec.deny_all, null)
  }] : []
  is_list_constraint    = each.value.is_list_constraint
}

module "org_custom_constraints" {
  source = "./modules/org_policy_custom_constraint"

  for_each = local.custom_policies

  name         = each.value.name
  display_name = each.value.display_name
  description  = each.value.description
  parent       = local.parent

  condition     = each.value.transformed_spec.condition
  action_type   = each.value.transformed_spec.action_type
  method_types  = try(each.value.transformed_spec.method_types, "CREATE,UPDATE")
  resource_type = each.value.transformed_spec.resource_type
}