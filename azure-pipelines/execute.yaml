trigger: none

appendCommitMessageToRunName: false
# name: $(date:yyyyMMdd)$(rev:.r) • ${{ parameters.environment }} • ${{ coalesce(split(parameters.example, '/')[2], split(parameters.example, '/')[1]) }} • ${{ parameters.action }}
name: $(date:yyyyMMdd)$(rev:.r) • ${{ parameters.environment }} • ${{ parameters.example }} • ${{ parameters.action }}

parameters:
- name: environment
  type: string
  default: EDV-gcp-iactest
  values:
  - EDV-gcp-iactest
  - STT-gcp-iactest
  - RPR-gcp-iactest
  - RPD-gcp-iactest
  # - DEV-gcp-iactest
  # - TST-gcp-iactest
  # - PPR-gcp-iactest
  # - PRD-gcp-iactest


- name: example
  type: string
  default: 01-default
  values:
  - 01-default

- name: action
  type: string
  default: plan
  values:
    - plan
    - apply
    - destroy
    - test

- name: skip_checkov
  type: boolean
  default: true

- name: timeout_in_minutes
  type: number
  default: 60

- name: no_proxy
  type: string
  default: ' '

- name: terraform_version
  type: string
  default: 1.12.2
  values:
  - 1.12.2
  - 1.11.4
  - 1.10.5
  - 1.9.8

- name: terraformUnlockStateLockID
  type: string
  default: ' '

variables:
  - group: 'Centrally managed variable group'
  - template: env/${{ parameters.environment }}.yaml@tooling
  # - template: env/${{ parameters.environment }}.yaml@pipelines

resources:
  repositories:
    # - repository: pipelines
    #   type: git
    #   name: gcp-lz/pipelines
    #   ref: v0100 #refs/tags/v0.9
    - repository: tooling
      type: git
      name: tooling
      ref: v510
    - repository: pipelinetemplates
      type: git
      name: OTPHU-CDO-ADOS-TOOLS/pipelinetemplates
      ref: refs/tags/v7

extends:
  template: iac-pipelines/iac-execute.yaml@tooling
  parameters:
    cloud: gcp
    action: ${{ parameters.action }}
    environment: ${{ variables.environment }}
    terraformGlobalTFVars: ${{ parameters.environment }}
    appCode: ${{ variables.appCode }}
    terraformProjectLocation: examples/${{ parameters.example }}
    terraformExtraNoProxy: ${{ parameters.no_proxy }}
    timeoutInMinutes: ${{ parameters.timeout_in_minutes }}
    terraformUnlockStateLockID: ${{ parameters.terraformUnlockStateLockID }}
    terraformRCFileForNetworkMirror: "network-mirror/.terraformrc"
    skipCheckovScan: ${{ parameters.skip_checkov }}
    googleCloudProject: ${{ variables.googleCloudProject }}
    googleCloudRegion: ${{ variables.googleCloudRegion }}
    googleCloudKeyFile: ${{ variables.googleCloudKeyFile }}
    googleBucketName: ${{ variables.googleBucketName }}
    googleImpersonateServiceAccount: ${{ variables.googleImpersonateServiceAccount }}
    terraformVersion: ${{ parameters.terraform_version }}

# extends:
#   template: iac-pipelines/iac-execute.yaml@pipelines
#   parameters:
#     # Common
#     action: ${{ parameters.action }}
#     environment: ${{ variables.environment }}
#     appCode: ${{ variables.appCode }}
#     timeoutInMinutes: ${{ parameters.timeout_in_minutes }}
#     # Terraform
#     terraformProjectLocation: examples/${{ parameters.example }}
#     terraformGlobalTFVars: ${{ parameters.environment }}
#     terraformExtraNoProxy: ${{ parameters.no_proxy }}
#     terraformUnlockStateLockID: ${{ parameters.terraformUnlockStateLockID }}
#     # terraformRCFileForNetworkMirror: "network-mirror/.terraformrc"
#     terraformVersion: ${{ parameters.terraform_version }}
#     # Checkov
#     skipCheckovScan: ${{ parameters.skip_checkov }}
#     # GCP Login and Backend
#     googleCloudProject: ${{ variables.googleCloudProject }}
#     googleCloudRegion: ${{ variables.googleCloudRegion }}
#     googleCloudKeyFile: ${{ variables.googleCloudKeyFile }}
#     googleBucketName: ${{ variables.googleBucketName }}
#     googleImpersonateServiceAccount: ${{ variables.googleImpersonateServiceAccount }}
#     # Azure OIDC Login
#     armServiceConnection: ${{ variables.armServiceConnection }}
#     subscriptionID: ${{ variables.subscriptionID }}
#     # # Config step parameters
#     # enableConfigStep: true
#     # config_directory: policy/${{ parameters.state }}
#     # config_directory_prod: policy/${{ parameters.state }}
#     # config_repo: config