trigger: none

appendCommitMessageToRunName: false
name: $(date:yyyyMMdd)$(rev:.r) • ${{ parameters.environment }} • ${{ parameters.example }} • ${{ parameters.action }}

parameters:
- name: environment
  type: string
  default: STT-gcp-iactest
  values:
  - STT-gcp-iactest

- name: example
  type: string
  default: 01-default
  values:
  - 01-default

- name: action
  type: string
  default: test
  values:
    - test

- name: skip_checkov
  type: boolean
  default: true

- name: timeout_in_minutes
  type: number
  default: 60

- name: no_proxy
  type: string
  default: ' '

- name: terraform_state_lock_id
  type: string
  default: ' '

variables:
  - group: 'Centrally managed variable group'
  - template: env/${{ parameters.environment }}.yaml@tooling

resources:
  repositories:
    - repository: tooling
      type: git
      name: tooling
      ref: refs/tags/v5.0.0
    - repository: pipelinetemplates
      type: git
      name: OTPHU-CDO-ADOS-TOOLS/pipelinetemplates
      ref: refs/tags/v7

extends:
  template: iac-pipelines/iac-execute.yaml@tooling
  parameters:
    cloud: gcp
    action: ${{ parameters.action }}
    environment: ${{ variables.environment }}
    environmentConfig: ${{ parameters.environment }}
    appCode: ${{ variables.appCode }}
    terraformProjectLocation: examples/${{ parameters.example }}
    terraformExtraNoProxy: ${{ parameters.no_proxy }}
    timeoutInMinutes: ${{ parameters.timeout_in_minutes }}
    terraformUnlockStateLockID: ${{ parameters.terraform_state_lock_id }}
    terraformRCFileForNetworkMirror: "network-mirror/.terraformrc"
    skipCheckovScan: ${{ parameters.skip_checkov }}
    googleCloudProject: ${{ variables.googleCloudProject }}
    googleCloudRegion: ${{ variables.googleCloudRegion }}
    googleCloudKeyFile: ${{ variables.googleCloudKeyFile }}
    googleBucketName: ${{ variables.googleBucketName }}
    googleImpersonateServiceAccount: ${{ variables.googleImpersonateServiceAccount }}
    terraformVersion: '1.11.1'