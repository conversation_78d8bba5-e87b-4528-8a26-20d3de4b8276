provider "google" {
  project = "test-project"
  region  = "us-central1"
  credentials = jsonencode({
    "client_id" : "test",
    "client_secret" : "test",
    "refresh_token" : "test",
    "type" : "authorized_user"
  })
}

run "plan_with_org_policies" {
  command = plan

  variables {
    folder_id = "336467911323"
    target    = "lztest"
    context = {
      project = "testproject"
    }

    policies = {

      disable_ssh_in_browser = {
        type            = "BuiltIn"
        constraint_name = "compute.managed.restrictDiskCreation"
        spec = {
          default = {
            enforce    = "true"
            parameters = jsonencode({ "isSizeLimitCheck" : true, "allowedDiskTypes" : ["pd-ssd", "pd-standard"] })

          }
          org     = {}
          audit   = {}
          core    = {}
          lzprod  = {}
          lztest  = {} # Will use default enforce = "TRUE"
          managmt = {}
          network = {
            enforce = "false"
          }
        }
      },

      domain_restricted_sharing = {
        type            = "BuiltIn"
        constraint_name = "iam.allowedPolicyMemberDomains"
        spec = {
          default = {
            allow_all           = "TRUE"
            denied_values       = "principalSet://iam.googleapis.com/folders/336467911323, C02nnx01r"
            inherit_from_parent = "TRUE"
          }
          org    = {}
          audit  = {}
          core   = {}
          lzprod = {}
          lztest = {
            inherit_from_parent = "FALSE" # Override for lztest
            exclude_projects    = "otp-diszi-demoday-prj-edv-01, otp-diszi-iactest-prj-edv-01"
          }
          managmt = {}
          network = {}
        }
      },

      enforce_uniform_bucket_access_conditional = {
        type            = "BuiltIn"
        constraint_name = "storage.uniformBucketLevelAccess"
        spec = {
          default = {
            enforce   = "TRUE"
            condition = "has(resource.labels.environment) && resource.labels.environment in ['prod', 'staging']"
          }
          org     = {}
          audit   = {}
          core    = {}
          lzprod  = {}
          lztest  = {}
          managmt = {}
          network = {}
        }
      },

      example_policy_not_for_audit = {
        type            = "BuiltIn"
        constraint_name = "compute.disableSerialPortLogging"
        spec = {
          default = {
            enforce = "TRUE"
          }
          org    = {}
          audit  = {}
          core   = {}
          lzprod = {}
          # lztest  = {} # Skip this policy for lztest
          managmt = {}
          network = {}
        }
      }

    }
  }
}
