provider "google" {
  project = "test-project"
  region  = "us-central1"
  credentials = jsonencode({
    "client_id" : "test",
    "client_secret" : "test",
    "refresh_token" : "test",
    "type" : "authorized_user"
  })
}

run "plan_with_org_policies" {
  command = plan

  variables {
    organization_id = "226353376208"
    target          = "org"
    context = {
      project = "testproject"
    }

    policies = {

      disable_ssh_in_browser = {
        type            = "BuiltIn"
        constraint_name = "compute.disableSshInBrowser"
        spec = {
          default = {
            enforce = "true"
          }
          org = {
            exclude_folders  = "911817463245, 741641675166"
            exclude_projects = "otp-diszi-demoday-prj-edv-01"
          }
          audit   = {}
          core    = {}
          lzprod  = {}
          lztest  = {}
          managmt = {}
          network = {
            enforce = "false"
          }
        }
      },

      deny_external_load_balancers = {
        type            = "BuiltIn"
        constraint_name = "compute.restrictLoadBalancerCreationForTypes"
        spec = {
          default = {
            denied_values = "EXTERNAL,"
            deny_all      = "FALSE"
          }
          org     = {} # Will use default
          audit   = {}
          core    = {}
          lzprod  = {}
          lztest  = {}
          managmt = {}
          network = {}
        }
      },

      restrict_vm_external_ip_conditional = {
        type            = "BuiltIn"
        constraint_name = "compute.vmExternalIpAccess"
        spec = {
          default = {
            denied_values = "*"
            condition     = "resource.matchTagId('tagKeys/123', 'tagValues/456')"
          }
          org     = {} # Skip this policy for org
          audit   = {}
          core    = {}
          lzprod  = {}
          lztest  = {}
          managmt = {}
          network = {}
        }
      }
      
    }
  }
}
