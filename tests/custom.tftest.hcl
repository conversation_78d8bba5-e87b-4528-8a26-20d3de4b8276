provider "google" {
  project = "test-project"
  region  = "us-central1"
  credentials = jsonencode({
    "client_id" : "test",
    "client_secret" : "test",
    "refresh_token" : "test",
    "type" : "authorized_user"
  })
}

run "plan_with_org_policies" {
  command = plan

  variables {
    organization_id = "226353376208"
    target          = "org"
    context = {
      project = "testproject"
    }

    policies = {

      require_gcs_bucket_labels = {
        type         = "Custom"
        name         = "custom.requireGCSBucketLabels"
        display_name = "Require GCS Bucket Labels"
        description  = "This constraint requires that all GCS buckets have specific labels: environment, team, and cost-center."
        spec = {
          default = {
            # CEL condition that ensures required labels are present
            condition = <<-EOT
            !has(resource.labels.environment) || 
            !has(resource.labels.team) || 
            !has(resource.labels['cost-center']) ||
            resource.labels.environment == "" ||
            resource.labels.team == "" ||
            resource.labels['cost-center'] == ""
          EOT

            # DENY if the required labels are missing or empty
            action_type = "DENY"

            # Apply to CREATE and UPDATE operations
            method_types = "CREATE, UPDATE"

            # Resource type this constraint applies to
            resource_type = "storage.googleapis.com/Bucket"
          }
          org     = {}
          audit   = {}
          core    = {}
          lzprod  = {}
          lztest  = {} # Will use default values
          managmt = {}
          network = {}
        }
      },

      enforce_gcs_bucket_labels = {
        type            = "BuiltIn"
        constraint_name = "custom.requireGCSBucketLabels"
        spec = {
          default = {
            enforce = "true"
          }
          org     = {}
          audit   = {}
          core    = {}
          lzprod  = {}
          lztest  = {} # Will use default enforce = "true"
          managmt = {}
          network = {}
        }
      }

    }
  }
}
