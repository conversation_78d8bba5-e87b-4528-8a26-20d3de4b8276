variable "name" {
  type        = string
  description = "(Required) Immutable. The name of the custom constraint. This is unique within the organization."

  validation {
    condition     = length(trimspace(var.name)) > 0
    error_message = "The 'name' variable cannot be empty."
  }
}

variable "display_name" {
  type        = string
  description = "(Optional) A human-friendly name for the constraint."
}

variable "description" {
  type        = string
  description = "(Optional) A human-friendly description of the constraint to display as an error message when the policy is violated."
}

variable "parent" {
  type        = string
  description = "(Required) The parent of the resource, an organization. Format should be organizations/{organization_id}."

  validation {
    condition     = can(regex("^organizations/[0-9]+$", var.parent))
    error_message = "The 'parent' variable must be in the format 'organizations/{organization_id}'."
  }
}

variable "condition" {
  type        = string
  description = "(Required) A CEL condition that refers to a supported service resource, for example resource.management.autoUpgrade == false."

  validation {
    condition     = length(trimspace(var.condition)) > 0
    error_message = "The 'condition' variable cannot be empty."
  }
}

variable "action_type" {
  type        = string
  description = "(Required) The action to take if the condition is met. Possible values are: ALLOW, DENY."

  validation {
    condition     = can(regex("^(ALLOW|DENY)$", var.action_type))
    error_message = "The action_type variable must be either 'ALLOW' or 'DENY'."
  }
}

variable "method_types" {
  type        = list(string)
  description = "(Required) A list of RESTful methods for which to enforce the constraint. Can be CREATE, UPDATE, or both."
  default     = ["CREATE", "UPDATE"]

  validation {
    condition = alltrue([
      for method in var.method_types : contains(["CREATE", "UPDATE"], method)
    ])
    error_message = "Method types must be one of: CREATE, UPDATE."
  }
}

variable "resource_type" {
  type        = string
  description = "(Required) Immutable. The fully qualified name of the Google Cloud REST resource containing the object and field you want to restrict. For example, container.googleapis.com/NodePool."

  validation {
    condition     = length(trimspace(var.resource_type)) > 0
    error_message = "The 'resource_type' variable cannot be empty."
  }
}