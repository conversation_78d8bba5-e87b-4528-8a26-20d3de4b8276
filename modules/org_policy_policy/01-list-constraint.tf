resource "google_org_policy_policy" "policy_list" {
  count  = var.is_list_constraint ? 1 : 0
  name   = local.policy_name
  parent = var.parent

  spec {
    inherit_from_parent = var.inherit_from_parent
    rules {
      values {
        allowed_values = var.is_list_constraint ? var.list_constraint[0].allowed_values : null
        denied_values  = var.is_list_constraint ? var.list_constraint[0].denied_values : null
      }
      allow_all = var.is_list_constraint ? var.list_constraint[0].allow_all : null
      deny_all  = var.is_list_constraint ? var.list_constraint[0].deny_all : null

      dynamic "condition" {
        for_each = var.condition != null ? [var.condition] : []
        content {
          expression  = condition.value.expression
          title       = condition.value.title
          description = condition.value.description
          location    = condition.value.location
        }
      }
    }
  }
}

resource "google_org_policy_policy" "exception_list_folders" {
  for_each = var.is_list_constraint ? toset(var.exclude_folders) : []
  name     = "folders/${each.value}/policies/${var.constraint_name}"
  parent   = "folders/${each.value}"

  spec {
    reset = true
  }
}

resource "google_org_policy_policy" "exception_list_projects" {
  for_each = var.is_list_constraint ? toset(var.exclude_projects) : []
  name     = "projects/${each.value}/policies/${var.constraint_name}"
  parent   = "projects/${each.value}"

  spec {
    reset = true
  }
}