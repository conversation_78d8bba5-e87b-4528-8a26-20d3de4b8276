resource "google_org_policy_policy" "policy_boolean" {
  count  = var.is_boolean_constraint ? 1 : 0
  name   = local.policy_name
  parent = var.parent

  spec {
    rules {
      enforce    = var.enforce_policy
      parameters = var.parameters

      dynamic "condition" {
        for_each = var.condition != null ? [var.condition] : []
        content {
          expression  = condition.value.expression
          title       = condition.value.title
          description = condition.value.description
          location    = condition.value.location
        }
      }
    }
  }
}

resource "google_org_policy_policy" "exception_boolean_folders" {
  for_each = var.is_boolean_constraint ? toset(var.exclude_folders) : []
  name     = "folders/${each.value}/policies/${var.constraint_name}"
  parent   = "folders/${each.value}"

  spec {
    rules {
      enforce = "FALSE"
    }
  }
}

resource "google_org_policy_policy" "exception_boolean_projects" {
  for_each = var.is_boolean_constraint ? toset(var.exclude_projects) : []
  name     = "projects/${each.value}/policies/${var.constraint_name}"
  parent   = "projects/${each.value}"

  spec {
    rules {
      enforce = "FALSE"
    }
  }
}