### GENERIC VARIABLES
variable "parent" {
  description = "(Required) The parent resource for the policy (e.g., organizations/123, folders/456, projects/789)."
  type        = string

  validation {
    condition     = var.parent != null && length(trimspace(var.parent)) > 0
    error_message = "parent must be a non-empty string."
  }
}

variable "constraint_name" {
  description = "(Required) The name of the Policy/Constraint."
  type        = string

  validation {
    condition     = length(var.constraint_name) > 0
    error_message = "The constraint_name variable must not be null or empty."
  }
}

variable "condition" {
  description = "(Optional) A condition which determines whether this rule is used in the evaluation of the policy."
  type = object({
    expression  = optional(string)
    title       = optional(string)
    description = optional(string)
    location    = optional(string)
  })
  default = null
}

variable "exclude_folders" {
  description = "(Optional) List of folder resource IDs to exclude from the main policy and deploy exception policies for."
  type        = list(string)
  default     = []
}

variable "exclude_projects" {
  description = "(Optional) List of project resource IDs to exclude from the main policy and deploy exception policies for."
  type        = list(string)
  default     = []
}

### BOOLEAN CONSTRAINT VARIABLES
variable "enforce_policy" {
  description = "(Optional) If TRUE, then the Policy is enforced. If FALSE, then any configuration is acceptable."
  type        = string
  default     = null

  validation {
    condition     = var.enforce_policy == null || contains(["true", "false"], lower(var.enforce_policy))
    error_message = "enforce_policy must be either 'true', 'false', or null."
  }
}

variable "parameters" {
  description = "(Optional) Required for Managed Constraints if parameters defined in constraints. Pass parameter values when policy enforcement is enabled. Ensure that parameter value types match those defined in the constraint definition. For example: jsonencode({\"isSizeLimitCheck\" : true, \"allowedDiskTypes\" : [\"pd-ssd\", \"pd-standard\"]})."
  type        = string
  default     = null
}

### LIST CONSTRAINT VARIABLES
variable "inherit_from_parent" {
  description = "(Optional) Determines the inheritance behavior of this Policy."
  type        = bool
  default     = false
}

variable "list_constraint" {
  description = "(Optional) The variable options for a List Constraint."
  type = list(object({
    allowed_values = list(string)
    denied_values  = list(string)
    allow_all      = string
    deny_all       = string
  }))

  validation {
    condition = (
      var.list_constraint == null ||
      length(var.list_constraint) == 0 ||
      alltrue([
        for c in var.list_constraint : (
          (c.allow_all == null || contains(["true", "false"], lower(c.allow_all))) &&
          (c.deny_all == null || contains(["true", "false"], lower(c.deny_all)))
        )
      ])
    )
    error_message = "list_constraint must be null, empty, or contain valid objects with allowed_values, denied_values, allow_all, and deny_all fields."
  }
}

variable "is_boolean_constraint" {
  type = bool
  default = false
}

variable "is_list_constraint" {
  type = bool
  default = false
}