variable "context" {
  type        = any
  description = "(Required) terraform-context module"

  validation {
    condition     = can(regex("^[a-z0-9]{2,}$", var.context.project))
    error_message = "Only lower case alphanumeric characters are allowed and should contain at least two characters."
  }
}


variable "labels" {
  description = "(Optional) Labels to apply to the Pub/Sub resources"
  type        = map(string)
  default     = null
}
